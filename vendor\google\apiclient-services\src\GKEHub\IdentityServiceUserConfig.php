<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\GKEHub;

class IdentityServiceUserConfig extends \Google\Model
{
  /**
   * @var string
   */
  public $baseDn;
  /**
   * @var string
   */
  public $filter;
  /**
   * @var string
   */
  public $idAttribute;
  /**
   * @var string
   */
  public $loginAttribute;

  /**
   * @param string
   */
  public function setBaseDn($baseDn)
  {
    $this->baseDn = $baseDn;
  }
  /**
   * @return string
   */
  public function getBaseDn()
  {
    return $this->baseDn;
  }
  /**
   * @param string
   */
  public function setFilter($filter)
  {
    $this->filter = $filter;
  }
  /**
   * @return string
   */
  public function getFilter()
  {
    return $this->filter;
  }
  /**
   * @param string
   */
  public function setIdAttribute($idAttribute)
  {
    $this->idAttribute = $idAttribute;
  }
  /**
   * @return string
   */
  public function getIdAttribute()
  {
    return $this->idAttribute;
  }
  /**
   * @param string
   */
  public function setLoginAttribute($loginAttribute)
  {
    $this->loginAttribute = $loginAttribute;
  }
  /**
   * @return string
   */
  public function getLoginAttribute()
  {
    return $this->loginAttribute;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(IdentityServiceUserConfig::class, 'Google_Service_GKEHub_IdentityServiceUserConfig');
