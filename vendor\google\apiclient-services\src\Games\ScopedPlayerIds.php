<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Games;

class ScopedPlayerIds extends \Google\Model
{
  /**
   * @var string
   */
  public $developerPlayerKey;
  /**
   * @var string
   */
  public $gamePlayerId;

  /**
   * @param string
   */
  public function setDeveloperPlayerKey($developerPlayerKey)
  {
    $this->developerPlayerKey = $developerPlayerKey;
  }
  /**
   * @return string
   */
  public function getDeveloperPlayerKey()
  {
    return $this->developerPlayerKey;
  }
  /**
   * @param string
   */
  public function setGamePlayerId($gamePlayerId)
  {
    $this->gamePlayerId = $gamePlayerId;
  }
  /**
   * @return string
   */
  public function getGamePlayerId()
  {
    return $this->gamePlayerId;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ScopedPlayerIds::class, 'Google_Service_Games_ScopedPlayerIds');
