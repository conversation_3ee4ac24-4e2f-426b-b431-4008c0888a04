<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\GKEHub;

class AnthosVMSubFeatureState extends \Google\Model
{
  /**
   * @var string
   */
  public $description;
  /**
   * @var string
   */
  public $installationState;
  protected $migrateStateType = MigrateState::class;
  protected $migrateStateDataType = '';
  protected $serviceMeshStateType = ServiceMeshState::class;
  protected $serviceMeshStateDataType = '';

  /**
   * @param string
   */
  public function setDescription($description)
  {
    $this->description = $description;
  }
  /**
   * @return string
   */
  public function getDescription()
  {
    return $this->description;
  }
  /**
   * @param string
   */
  public function setInstallationState($installationState)
  {
    $this->installationState = $installationState;
  }
  /**
   * @return string
   */
  public function getInstallationState()
  {
    return $this->installationState;
  }
  /**
   * @param MigrateState
   */
  public function setMigrateState(MigrateState $migrateState)
  {
    $this->migrateState = $migrateState;
  }
  /**
   * @return MigrateState
   */
  public function getMigrateState()
  {
    return $this->migrateState;
  }
  /**
   * @param ServiceMeshState
   */
  public function setServiceMeshState(ServiceMeshState $serviceMeshState)
  {
    $this->serviceMeshState = $serviceMeshState;
  }
  /**
   * @return ServiceMeshState
   */
  public function getServiceMeshState()
  {
    return $this->serviceMeshState;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AnthosVMSubFeatureState::class, 'Google_Service_GKEHub_AnthosVMSubFeatureState');
