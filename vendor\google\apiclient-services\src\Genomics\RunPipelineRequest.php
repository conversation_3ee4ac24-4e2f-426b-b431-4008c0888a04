<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Genomics;

class RunPipelineRequest extends \Google\Model
{
  /**
   * @var string[]
   */
  public $labels;
  protected $pipelineType = Pipeline::class;
  protected $pipelineDataType = '';
  /**
   * @var string
   */
  public $pubSubTopic;

  /**
   * @param string[]
   */
  public function setLabels($labels)
  {
    $this->labels = $labels;
  }
  /**
   * @return string[]
   */
  public function getLabels()
  {
    return $this->labels;
  }
  /**
   * @param Pipeline
   */
  public function setPipeline(Pipeline $pipeline)
  {
    $this->pipeline = $pipeline;
  }
  /**
   * @return Pipeline
   */
  public function getPipeline()
  {
    return $this->pipeline;
  }
  /**
   * @param string
   */
  public function setPubSubTopic($pubSubTopic)
  {
    $this->pubSubTopic = $pubSubTopic;
  }
  /**
   * @return string
   */
  public function getPubSubTopic()
  {
    return $this->pubSubTopic;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(RunPipelineRequest::class, 'Google_Service_Genomics_RunPipelineRequest');
