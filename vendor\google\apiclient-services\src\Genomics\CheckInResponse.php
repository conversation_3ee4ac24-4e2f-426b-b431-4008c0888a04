<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Genomics;

class CheckInResponse extends \Google\Model
{
  /**
   * @var string
   */
  public $deadline;
  /**
   * @var array[]
   */
  public $features;
  /**
   * @var array[]
   */
  public $metadata;

  /**
   * @param string
   */
  public function setDeadline($deadline)
  {
    $this->deadline = $deadline;
  }
  /**
   * @return string
   */
  public function getDeadline()
  {
    return $this->deadline;
  }
  /**
   * @param array[]
   */
  public function setFeatures($features)
  {
    $this->features = $features;
  }
  /**
   * @return array[]
   */
  public function getFeatures()
  {
    return $this->features;
  }
  /**
   * @param array[]
   */
  public function setMetadata($metadata)
  {
    $this->metadata = $metadata;
  }
  /**
   * @return array[]
   */
  public function getMetadata()
  {
    return $this->metadata;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(CheckInResponse::class, 'Google_Service_Genomics_CheckInResponse');
