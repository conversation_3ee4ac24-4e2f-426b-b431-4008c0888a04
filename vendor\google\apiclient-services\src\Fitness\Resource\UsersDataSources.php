<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Fitness\Resource;

use Google\Service\Fitness\DataSource;
use Google\Service\Fitness\ListDataSourcesResponse;

/**
 * The "dataSources" collection of methods.
 * Typical usage is:
 *  <code>
 *   $fitnessService = new Google\Service\Fitness(...);
 *   $dataSources = $fitnessService->users_dataSources;
 *  </code>
 */
class UsersDataSources extends \Google\Service\Resource
{
  /**
   * Creates a new data source that is unique across all data sources belonging to
   * this user. A data source is a unique source of sensor data. Data sources can
   * expose raw data coming from hardware sensors on local or companion devices.
   * They can also expose derived data, created by transforming or merging other
   * data sources. Multiple data sources can exist for the same data type. Every
   * data point in every dataset inserted into or read from the Fitness API has an
   * associated data source. Each data source produces a unique stream of dataset
   * updates, with a unique data source identifier. Not all changes to data source
   * affect the data stream ID, so that data collected by updated versions of the
   * same application/device can still be considered to belong to the same data
   * source. Data sources are identified using a string generated by the server,
   * based on the contents of the source being created. The dataStreamId field
   * should not be set when invoking this method. It will be automatically
   * generated by the server with the correct format. If a dataStreamId is set, it
   * must match the format that the server would generate. This format is a
   * combination of some fields from the data source, and has a specific order. If
   * it doesn't match, the request will fail with an error. Specifying a DataType
   * which is not a known type (beginning with "com.google.") will create a
   * DataSource with a *custom data type*. Custom data types are only readable by
   * the application that created them. Custom data types are *deprecated*; use
   * standard data types instead. In addition to the data source fields included
   * in the data source ID, the developer project number that is authenticated
   * when creating the data source is included. This developer project number is
   * obfuscated when read by any other developer reading public data types.
   * (dataSources.create)
   *
   * @param string $userId Create the data source for the person identified. Use
   * me to indicate the authenticated user. Only me is supported at this time.
   * @param DataSource $postBody
   * @param array $optParams Optional parameters.
   * @return DataSource
   * @throws \Google\Service\Exception
   */
  public function create($userId, DataSource $postBody, $optParams = [])
  {
    $params = ['userId' => $userId, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('create', [$params], DataSource::class);
  }
  /**
   * Deletes the specified data source. The request will fail if the data source
   * contains any data points. (dataSources.delete)
   *
   * @param string $userId Retrieve a data source for the person identified. Use
   * me to indicate the authenticated user. Only me is supported at this time.
   * @param string $dataSourceId The data stream ID of the data source to delete.
   * @param array $optParams Optional parameters.
   * @return DataSource
   * @throws \Google\Service\Exception
   */
  public function delete($userId, $dataSourceId, $optParams = [])
  {
    $params = ['userId' => $userId, 'dataSourceId' => $dataSourceId];
    $params = array_merge($params, $optParams);
    return $this->call('delete', [$params], DataSource::class);
  }
  /**
   * Returns the specified data source. (dataSources.get)
   *
   * @param string $userId Retrieve a data source for the person identified. Use
   * me to indicate the authenticated user. Only me is supported at this time.
   * @param string $dataSourceId The data stream ID of the data source to
   * retrieve.
   * @param array $optParams Optional parameters.
   * @return DataSource
   * @throws \Google\Service\Exception
   */
  public function get($userId, $dataSourceId, $optParams = [])
  {
    $params = ['userId' => $userId, 'dataSourceId' => $dataSourceId];
    $params = array_merge($params, $optParams);
    return $this->call('get', [$params], DataSource::class);
  }
  /**
   * Lists all data sources that are visible to the developer, using the OAuth
   * scopes provided. The list is not exhaustive; the user may have private data
   * sources that are only visible to other developers, or calls using other
   * scopes. (dataSources.listUsersDataSources)
   *
   * @param string $userId List data sources for the person identified. Use me to
   * indicate the authenticated user. Only me is supported at this time.
   * @param array $optParams Optional parameters.
   *
   * @opt_param string dataTypeName The names of data types to include in the
   * list. If not specified, all data sources will be returned.
   * @return ListDataSourcesResponse
   * @throws \Google\Service\Exception
   */
  public function listUsersDataSources($userId, $optParams = [])
  {
    $params = ['userId' => $userId];
    $params = array_merge($params, $optParams);
    return $this->call('list', [$params], ListDataSourcesResponse::class);
  }
  /**
   * Updates the specified data source. The dataStreamId, dataType, type,
   * dataStreamName, and device properties with the exception of version, cannot
   * be modified. Data sources are identified by their dataStreamId.
   * (dataSources.update)
   *
   * @param string $userId Update the data source for the person identified. Use
   * me to indicate the authenticated user. Only me is supported at this time.
   * @param string $dataSourceId The data stream ID of the data source to update.
   * @param DataSource $postBody
   * @param array $optParams Optional parameters.
   * @return DataSource
   * @throws \Google\Service\Exception
   */
  public function update($userId, $dataSourceId, DataSource $postBody, $optParams = [])
  {
    $params = ['userId' => $userId, 'dataSourceId' => $dataSourceId, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('update', [$params], DataSource::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(UsersDataSources::class, 'Google_Service_Fitness_Resource_UsersDataSources');
