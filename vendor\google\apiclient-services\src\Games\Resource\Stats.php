<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Games\Resource;

use Google\Service\Games\StatsResponse;

/**
 * The "stats" collection of methods.
 * Typical usage is:
 *  <code>
 *   $gamesService = new Google\Service\Games(...);
 *   $stats = $gamesService->stats;
 *  </code>
 */
class Stats extends \Google\Service\Resource
{
  /**
   * Returns engagement and spend statistics in this application for the currently
   * authenticated user. (stats.get)
   *
   * @param array $optParams Optional parameters.
   * @return StatsResponse
   * @throws \Google\Service\Exception
   */
  public function get($optParams = [])
  {
    $params = [];
    $params = array_merge($params, $optParams);
    return $this->call('get', [$params], StatsResponse::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Stats::class, 'Google_Service_Games_Resource_Stats');
