<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\FirebaseRules;

class TestResult extends \Google\Collection
{
  protected $collection_key = 'visitedExpressions';
  /**
   * @var string[]
   */
  public $debugMessages;
  protected $errorPositionType = SourcePosition::class;
  protected $errorPositionDataType = '';
  protected $expressionReportsType = ExpressionReport::class;
  protected $expressionReportsDataType = 'array';
  protected $functionCallsType = FunctionCall::class;
  protected $functionCallsDataType = 'array';
  /**
   * @var string
   */
  public $state;
  protected $visitedExpressionsType = VisitedExpression::class;
  protected $visitedExpressionsDataType = 'array';

  /**
   * @param string[]
   */
  public function setDebugMessages($debugMessages)
  {
    $this->debugMessages = $debugMessages;
  }
  /**
   * @return string[]
   */
  public function getDebugMessages()
  {
    return $this->debugMessages;
  }
  /**
   * @param SourcePosition
   */
  public function setErrorPosition(SourcePosition $errorPosition)
  {
    $this->errorPosition = $errorPosition;
  }
  /**
   * @return SourcePosition
   */
  public function getErrorPosition()
  {
    return $this->errorPosition;
  }
  /**
   * @param ExpressionReport[]
   */
  public function setExpressionReports($expressionReports)
  {
    $this->expressionReports = $expressionReports;
  }
  /**
   * @return ExpressionReport[]
   */
  public function getExpressionReports()
  {
    return $this->expressionReports;
  }
  /**
   * @param FunctionCall[]
   */
  public function setFunctionCalls($functionCalls)
  {
    $this->functionCalls = $functionCalls;
  }
  /**
   * @return FunctionCall[]
   */
  public function getFunctionCalls()
  {
    return $this->functionCalls;
  }
  /**
   * @param string
   */
  public function setState($state)
  {
    $this->state = $state;
  }
  /**
   * @return string
   */
  public function getState()
  {
    return $this->state;
  }
  /**
   * @param VisitedExpression[]
   */
  public function setVisitedExpressions($visitedExpressions)
  {
    $this->visitedExpressions = $visitedExpressions;
  }
  /**
   * @return VisitedExpression[]
   */
  public function getVisitedExpressions()
  {
    return $this->visitedExpressions;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(TestResult::class, 'Google_Service_FirebaseRules_TestResult');
