<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\GKEHub;

class IdentityServiceAzureADConfig extends \Google\Model
{
  /**
   * @var string
   */
  public $clientId;
  /**
   * @var string
   */
  public $clientSecret;
  /**
   * @var string
   */
  public $encryptedClientSecret;
  /**
   * @var string
   */
  public $groupFormat;
  /**
   * @var string
   */
  public $kubectlRedirectUri;
  /**
   * @var string
   */
  public $tenant;
  /**
   * @var string
   */
  public $userClaim;

  /**
   * @param string
   */
  public function setClientId($clientId)
  {
    $this->clientId = $clientId;
  }
  /**
   * @return string
   */
  public function getClientId()
  {
    return $this->clientId;
  }
  /**
   * @param string
   */
  public function setClientSecret($clientSecret)
  {
    $this->clientSecret = $clientSecret;
  }
  /**
   * @return string
   */
  public function getClientSecret()
  {
    return $this->clientSecret;
  }
  /**
   * @param string
   */
  public function setEncryptedClientSecret($encryptedClientSecret)
  {
    $this->encryptedClientSecret = $encryptedClientSecret;
  }
  /**
   * @return string
   */
  public function getEncryptedClientSecret()
  {
    return $this->encryptedClientSecret;
  }
  /**
   * @param string
   */
  public function setGroupFormat($groupFormat)
  {
    $this->groupFormat = $groupFormat;
  }
  /**
   * @return string
   */
  public function getGroupFormat()
  {
    return $this->groupFormat;
  }
  /**
   * @param string
   */
  public function setKubectlRedirectUri($kubectlRedirectUri)
  {
    $this->kubectlRedirectUri = $kubectlRedirectUri;
  }
  /**
   * @return string
   */
  public function getKubectlRedirectUri()
  {
    return $this->kubectlRedirectUri;
  }
  /**
   * @param string
   */
  public function setTenant($tenant)
  {
    $this->tenant = $tenant;
  }
  /**
   * @return string
   */
  public function getTenant()
  {
    return $this->tenant;
  }
  /**
   * @param string
   */
  public function setUserClaim($userClaim)
  {
    $this->userClaim = $userClaim;
  }
  /**
   * @return string
   */
  public function getUserClaim()
  {
    return $this->userClaim;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(IdentityServiceAzureADConfig::class, 'Google_Service_GKEHub_IdentityServiceAzureADConfig');
