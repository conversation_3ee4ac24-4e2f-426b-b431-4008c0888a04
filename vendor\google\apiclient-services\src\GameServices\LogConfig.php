<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\GameServices;

class LogConfig extends \Google\Model
{
  protected $cloudAuditType = CloudAuditOptions::class;
  protected $cloudAuditDataType = '';
  protected $counterType = CounterOptions::class;
  protected $counterDataType = '';
  protected $dataAccessType = DataAccessOptions::class;
  protected $dataAccessDataType = '';

  /**
   * @param CloudAuditOptions
   */
  public function setCloudAudit(CloudAuditOptions $cloudAudit)
  {
    $this->cloudAudit = $cloudAudit;
  }
  /**
   * @return CloudAuditOptions
   */
  public function getCloudAudit()
  {
    return $this->cloudAudit;
  }
  /**
   * @param CounterOptions
   */
  public function setCounter(CounterOptions $counter)
  {
    $this->counter = $counter;
  }
  /**
   * @return CounterOptions
   */
  public function getCounter()
  {
    return $this->counter;
  }
  /**
   * @param DataAccessOptions
   */
  public function setDataAccess(DataAccessOptions $dataAccess)
  {
    $this->dataAccess = $dataAccess;
  }
  /**
   * @return DataAccessOptions
   */
  public function getDataAccess()
  {
    return $this->dataAccess;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(LogConfig::class, 'Google_Service_GameServices_LogConfig');
