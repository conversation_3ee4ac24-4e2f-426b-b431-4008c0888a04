<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\FirebaseAppDistribution\Resource;

use Google\Service\FirebaseAppDistribution\GoogleFirebaseAppdistroV1UploadReleaseRequest;
use Google\Service\FirebaseAppDistribution\GoogleLongrunningOperation;

/**
 * The "media" collection of methods.
 * Typical usage is:
 *  <code>
 *   $firebaseappdistributionService = new Google\Service\FirebaseAppDistribution(...);
 *   $media = $firebaseappdistributionService->media;
 *  </code>
 */
class Media extends \Google\Service\Resource
{
  /**
   * Uploads a binary. Uploading a binary can result in a new release being
   * created, an update to an existing release, or a no-op if a release with the
   * same binary already exists. (media.upload)
   *
   * @param string $app The name of the app resource. Format:
   * `projects/{project_number}/apps/{app_id}`
   * @param GoogleFirebaseAppdistroV1UploadReleaseRequest $postBody
   * @param array $optParams Optional parameters.
   * @return GoogleLongrunningOperation
   * @throws \Google\Service\Exception
   */
  public function upload($app, GoogleFirebaseAppdistroV1UploadReleaseRequest $postBody, $optParams = [])
  {
    $params = ['app' => $app, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('upload', [$params], GoogleLongrunningOperation::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Media::class, 'Google_Service_FirebaseAppDistribution_Resource_Media');
