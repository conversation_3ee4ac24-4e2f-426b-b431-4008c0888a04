<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\GKEHub;

class AnthosVMSubFeatureSpec extends \Google\Model
{
  /**
   * @var bool
   */
  public $enabled;
  protected $migrateSpecType = MigrateSpec::class;
  protected $migrateSpecDataType = '';
  protected $serviceMeshSpecType = ServiceMeshSpec::class;
  protected $serviceMeshSpecDataType = '';

  /**
   * @param bool
   */
  public function setEnabled($enabled)
  {
    $this->enabled = $enabled;
  }
  /**
   * @return bool
   */
  public function getEnabled()
  {
    return $this->enabled;
  }
  /**
   * @param MigrateSpec
   */
  public function setMigrateSpec(MigrateSpec $migrateSpec)
  {
    $this->migrateSpec = $migrateSpec;
  }
  /**
   * @return MigrateSpec
   */
  public function getMigrateSpec()
  {
    return $this->migrateSpec;
  }
  /**
   * @param ServiceMeshSpec
   */
  public function setServiceMeshSpec(ServiceMeshSpec $serviceMeshSpec)
  {
    $this->serviceMeshSpec = $serviceMeshSpec;
  }
  /**
   * @return ServiceMeshSpec
   */
  public function getServiceMeshSpec()
  {
    return $this->serviceMeshSpec;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AnthosVMSubFeatureSpec::class, 'Google_Service_GKEHub_AnthosVMSubFeatureSpec');
