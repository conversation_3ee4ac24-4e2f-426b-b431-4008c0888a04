<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\FirebaseDynamicLinks;

class AnalyticsInfo extends \Google\Model
{
  protected $googlePlayAnalyticsType = GooglePlayAnalytics::class;
  protected $googlePlayAnalyticsDataType = '';
  protected $itunesConnectAnalyticsType = ITunesConnectAnalytics::class;
  protected $itunesConnectAnalyticsDataType = '';

  /**
   * @param GooglePlayAnalytics
   */
  public function setGooglePlayAnalytics(GooglePlayAnalytics $googlePlayAnalytics)
  {
    $this->googlePlayAnalytics = $googlePlayAnalytics;
  }
  /**
   * @return GooglePlayAnalytics
   */
  public function getGooglePlayAnalytics()
  {
    return $this->googlePlayAnalytics;
  }
  /**
   * @param ITunesConnectAnalytics
   */
  public function setItunesConnectAnalytics(ITunesConnectAnalytics $itunesConnectAnalytics)
  {
    $this->itunesConnectAnalytics = $itunesConnectAnalytics;
  }
  /**
   * @return ITunesConnectAnalytics
   */
  public function getItunesConnectAnalytics()
  {
    return $this->itunesConnectAnalytics;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AnalyticsInfo::class, 'Google_Service_FirebaseDynamicLinks_AnalyticsInfo');
