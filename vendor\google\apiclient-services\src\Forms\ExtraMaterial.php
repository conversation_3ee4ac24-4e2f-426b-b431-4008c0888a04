<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Forms;

class ExtraMaterial extends \Google\Model
{
  protected $linkType = TextLink::class;
  protected $linkDataType = '';
  protected $videoType = VideoLink::class;
  protected $videoDataType = '';

  /**
   * @param TextLink
   */
  public function setLink(TextLink $link)
  {
    $this->link = $link;
  }
  /**
   * @return TextLink
   */
  public function getLink()
  {
    return $this->link;
  }
  /**
   * @param VideoLink
   */
  public function setVideo(VideoLink $video)
  {
    $this->video = $video;
  }
  /**
   * @return VideoLink
   */
  public function getVideo()
  {
    return $this->video;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ExtraMaterial::class, 'Google_Service_Forms_ExtraMaterial');
