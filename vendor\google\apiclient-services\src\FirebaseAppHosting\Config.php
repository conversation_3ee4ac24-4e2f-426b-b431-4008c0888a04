<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\FirebaseAppHosting;

class Config extends \Google\Collection
{
  protected $collection_key = 'env';
  protected $envType = EnvironmentVariable::class;
  protected $envDataType = 'array';
  protected $runConfigType = RunConfig::class;
  protected $runConfigDataType = '';

  /**
   * @param EnvironmentVariable[]
   */
  public function setEnv($env)
  {
    $this->env = $env;
  }
  /**
   * @return EnvironmentVariable[]
   */
  public function getEnv()
  {
    return $this->env;
  }
  /**
   * @param RunConfig
   */
  public function setRunConfig(RunConfig $runConfig)
  {
    $this->runConfig = $runConfig;
  }
  /**
   * @return RunConfig
   */
  public function getRunConfig()
  {
    return $this->runConfig;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Config::class, 'Google_Service_FirebaseAppHosting_Config');
