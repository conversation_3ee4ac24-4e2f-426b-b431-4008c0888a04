<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Firestore;

class GoogleFirestoreAdminV1UserCreds extends \Google\Model
{
  /**
   * @var string
   */
  public $createTime;
  /**
   * @var string
   */
  public $name;
  protected $resourceIdentityType = GoogleFirestoreAdminV1ResourceIdentity::class;
  protected $resourceIdentityDataType = '';
  /**
   * @var string
   */
  public $securePassword;
  /**
   * @var string
   */
  public $state;
  /**
   * @var string
   */
  public $updateTime;

  /**
   * @param string
   */
  public function setCreateTime($createTime)
  {
    $this->createTime = $createTime;
  }
  /**
   * @return string
   */
  public function getCreateTime()
  {
    return $this->createTime;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param GoogleFirestoreAdminV1ResourceIdentity
   */
  public function setResourceIdentity(GoogleFirestoreAdminV1ResourceIdentity $resourceIdentity)
  {
    $this->resourceIdentity = $resourceIdentity;
  }
  /**
   * @return GoogleFirestoreAdminV1ResourceIdentity
   */
  public function getResourceIdentity()
  {
    return $this->resourceIdentity;
  }
  /**
   * @param string
   */
  public function setSecurePassword($securePassword)
  {
    $this->securePassword = $securePassword;
  }
  /**
   * @return string
   */
  public function getSecurePassword()
  {
    return $this->securePassword;
  }
  /**
   * @param string
   */
  public function setState($state)
  {
    $this->state = $state;
  }
  /**
   * @return string
   */
  public function getState()
  {
    return $this->state;
  }
  /**
   * @param string
   */
  public function setUpdateTime($updateTime)
  {
    $this->updateTime = $updateTime;
  }
  /**
   * @return string
   */
  public function getUpdateTime()
  {
    return $this->updateTime;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleFirestoreAdminV1UserCreds::class, 'Google_Service_Firestore_GoogleFirestoreAdminV1UserCreds');
