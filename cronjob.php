<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
require __DIR__ . '/vendor/autoload.php';

$client = new \Google_Client();
$client->setApplicationName('Google Sheets API with PHP');
$client->setScopes([\Google_Service_Sheets::SPREADSHEETS]);
$client->setAuthConfig('odzleads-3fd4a44b7310.json');
$client->setAccessType('offline');

$service = new Google_Service_Sheets($client);

// Spreadsheet details
$spreadsheetId = '1IIF_g_GYLvQzD0QULsM4_fdJJG-i0nDJU_rwQmNSG7U';
$sheetName = 'Sheet15';
$range = $sheetName . '!F2:M'; // Read F (status) and M (date)

$response = $service->spreadsheets_values->get($spreadsheetId, $range);
$rows = $response->getValues();


$today = new DateTime();
$cutoffDate = (clone $today)->modify('-7 days');

foreach ($rows as $index => $row) {

    $status = strtolower(trim($row[0] ?? ''));
    $dateRaw = trim($row[6] ?? '');

    if ($status !== 'na' || empty($dateRaw)) {
        continue;
    }

    // Clean hidden characters and extra whitespace
    $dateRaw = preg_replace('/[\x00-\x1F\x7F\xA0]/u', '', $dateRaw);
  //  echo "<pre>"; print_r($dateRaw); die;

    // Try multiple formats
    $date = DateTime::createFromFormat('m/d/Y', $dateRaw) ?:
            DateTime::createFromFormat('d/m/Y', $dateRaw) ?:
            DateTime::createFromFormat('Y-m-d', $dateRaw);

    if (!$date) {
        echo "❌ Could not parse date in row " . ($index + 2) . ": [$dateRaw]\n";
        continue;
    }

    // For debug
    echo "🔍 Row " . ($index + 2) . ": Parsed Date = " . $date->format('Y-m-d') . " | Cutoff = " . $cutoffDate->format('Y-m-d') . "\n";

    if ($date <= $cutoffDate) {
        $rowNumber = $index + 2;
        $targetRange = "$sheetName!F$rowNumber";

        $valueRange = new Google_Service_Sheets_ValueRange([
            'range' => $targetRange,
            'values' => [['Not Converted']]
        ]);

        $params = ['valueInputOption' => 'RAW'];
        $service->spreadsheets_values->update($spreadsheetId, $targetRange, $valueRange, $params);

        echo "✅ Updated row $rowNumber: 'NA' -> 'Not Converted'\n";
    }
}
?>
