<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Firestore;

class GoogleFirestoreAdminV1IndexField extends \Google\Model
{
  /**
   * @var string
   */
  public $arrayConfig;
  /**
   * @var string
   */
  public $fieldPath;
  /**
   * @var string
   */
  public $order;
  protected $vectorConfigType = GoogleFirestoreAdminV1VectorConfig::class;
  protected $vectorConfigDataType = '';

  /**
   * @param string
   */
  public function setArrayConfig($arrayConfig)
  {
    $this->arrayConfig = $arrayConfig;
  }
  /**
   * @return string
   */
  public function getArrayConfig()
  {
    return $this->arrayConfig;
  }
  /**
   * @param string
   */
  public function setFieldPath($fieldPath)
  {
    $this->fieldPath = $fieldPath;
  }
  /**
   * @return string
   */
  public function getFieldPath()
  {
    return $this->fieldPath;
  }
  /**
   * @param string
   */
  public function setOrder($order)
  {
    $this->order = $order;
  }
  /**
   * @return string
   */
  public function getOrder()
  {
    return $this->order;
  }
  /**
   * @param GoogleFirestoreAdminV1VectorConfig
   */
  public function setVectorConfig(GoogleFirestoreAdminV1VectorConfig $vectorConfig)
  {
    $this->vectorConfig = $vectorConfig;
  }
  /**
   * @return GoogleFirestoreAdminV1VectorConfig
   */
  public function getVectorConfig()
  {
    return $this->vectorConfig;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleFirestoreAdminV1IndexField::class, 'Google_Service_Firestore_GoogleFirestoreAdminV1IndexField');
