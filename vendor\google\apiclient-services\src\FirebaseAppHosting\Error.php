<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\FirebaseAppHosting;

class Error extends \Google\Model
{
  /**
   * @var string
   */
  public $cloudResource;
  protected $errorType = Status::class;
  protected $errorDataType = '';
  /**
   * @var string
   */
  public $errorSource;

  /**
   * @param string
   */
  public function setCloudResource($cloudResource)
  {
    $this->cloudResource = $cloudResource;
  }
  /**
   * @return string
   */
  public function getCloudResource()
  {
    return $this->cloudResource;
  }
  /**
   * @param Status
   */
  public function setError(Status $error)
  {
    $this->error = $error;
  }
  /**
   * @return Status
   */
  public function getError()
  {
    return $this->error;
  }
  /**
   * @param string
   */
  public function setErrorSource($errorSource)
  {
    $this->errorSource = $errorSource;
  }
  /**
   * @return string
   */
  public function getErrorSource()
  {
    return $this->errorSource;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Error::class, 'Google_Service_FirebaseAppHosting_Error');
