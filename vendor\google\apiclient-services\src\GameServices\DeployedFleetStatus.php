<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\GameServices;

class DeployedFleetStatus extends \Google\Model
{
  /**
   * @var string
   */
  public $allocatedReplicas;
  /**
   * @var string
   */
  public $readyReplicas;
  /**
   * @var string
   */
  public $replicas;
  /**
   * @var string
   */
  public $reservedReplicas;

  /**
   * @param string
   */
  public function setAllocatedReplicas($allocatedReplicas)
  {
    $this->allocatedReplicas = $allocatedReplicas;
  }
  /**
   * @return string
   */
  public function getAllocatedReplicas()
  {
    return $this->allocatedReplicas;
  }
  /**
   * @param string
   */
  public function setReadyReplicas($readyReplicas)
  {
    $this->readyReplicas = $readyReplicas;
  }
  /**
   * @return string
   */
  public function getReadyReplicas()
  {
    return $this->readyReplicas;
  }
  /**
   * @param string
   */
  public function setReplicas($replicas)
  {
    $this->replicas = $replicas;
  }
  /**
   * @return string
   */
  public function getReplicas()
  {
    return $this->replicas;
  }
  /**
   * @param string
   */
  public function setReservedReplicas($reservedReplicas)
  {
    $this->reservedReplicas = $reservedReplicas;
  }
  /**
   * @return string
   */
  public function getReservedReplicas()
  {
    return $this->reservedReplicas;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(DeployedFleetStatus::class, 'Google_Service_GameServices_DeployedFleetStatus');
