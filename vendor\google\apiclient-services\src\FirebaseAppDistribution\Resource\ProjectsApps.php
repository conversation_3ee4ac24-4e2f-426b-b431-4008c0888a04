<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\FirebaseAppDistribution\Resource;

use Google\Service\FirebaseAppDistribution\GoogleFirebaseAppdistroV1AabInfo;

/**
 * The "apps" collection of methods.
 * Typical usage is:
 *  <code>
 *   $firebaseappdistributionService = new Google\Service\FirebaseAppDistribution(...);
 *   $apps = $firebaseappdistributionService->projects_apps;
 *  </code>
 */
class ProjectsApps extends \Google\Service\Resource
{
  /**
   * Gets Android App Bundle (AAB) information for a Firebase app.
   * (apps.getAabInfo)
   *
   * @param string $name Required. The name of the `AabInfo` resource to retrieve.
   * Format: `projects/{project_number}/apps/{app_id}/aabInfo`
   * @param array $optParams Optional parameters.
   * @return GoogleFirebaseAppdistroV1AabInfo
   * @throws \Google\Service\Exception
   */
  public function getAabInfo($name, $optParams = [])
  {
    $params = ['name' => $name];
    $params = array_merge($params, $optParams);
    return $this->call('getAabInfo', [$params], GoogleFirebaseAppdistroV1AabInfo::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ProjectsApps::class, 'Google_Service_FirebaseAppDistribution_Resource_ProjectsApps');
